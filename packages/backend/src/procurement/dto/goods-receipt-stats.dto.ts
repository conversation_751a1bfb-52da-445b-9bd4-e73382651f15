import { IsOptional, IsEnum, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Query DTO for goods receipt statistics endpoint
 */
export class GoodsReceiptStatsQueryDto {
  @ApiProperty({
    description: 'Time period for statistics calculation',
    enum: ['7d', '30d', '90d', 'quarter', 'year', 'all'],
    required: false,
    default: 'all',
  })
  @IsOptional()
  @IsEnum(['7d', '30d', '90d', 'quarter', 'year', 'all'])
  period?: '7d' | '30d' | '90d' | 'quarter' | 'year' | 'all';
}

/**
 * Response DTO for goods receipt statistics
 * This ensures proper validation and documentation of the API response
 */
export class GoodsReceiptStatsResponseDto {
  @ApiProperty({ description: 'Total number of goods receipts' })
  totalReceipts: number;

  @ApiProperty({ description: 'Number of receipts pending inspection' })
  pendingInspection: number;

  @ApiProperty({ description: 'Number of approved receipts' })
  approved: number;

  @ApiProperty({ description: 'Number of rejected receipts' })
  rejected: number;

  @ApiProperty({ description: 'Quality pass rate (0-1)', minimum: 0, maximum: 1 })
  qualityPassRate: number;

  @ApiProperty({ description: 'Average inspection time in hours' })
  averageInspectionTime: number;

  @ApiProperty({ description: 'Total value of all receipts' })
  totalValue: number;

  @ApiProperty({ description: 'Status statistics grouped by status' })
  statusStats: Record<string, number>;

  @ApiProperty({ description: 'Quality statistics grouped by quality status' })
  qualityStats: Record<string, number>;

  @ApiProperty({ description: 'Recent goods receipts', type: 'array' })
  recentReceipts: any[];

  @ApiProperty({ description: 'Quality trends over time', required: false })
  qualityTrends?: any[];

  @ApiProperty({ description: 'Supplier quality statistics', required: false })
  supplierQualityStats?: any[];

  @ApiProperty({ description: 'Processing time analytics', required: false })
  processingTimeAnalytics?: any;

  @ApiProperty({ description: 'Volume trends over time', required: false })
  volumeTrends?: any[];

  @ApiProperty({ description: 'Time period used for calculations' })
  period: string;
}
