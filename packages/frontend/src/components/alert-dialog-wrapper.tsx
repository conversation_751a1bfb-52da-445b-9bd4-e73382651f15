import type React from 'react';
import { useState, useEffect } from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';

type AlertDialogWrapperProps = {
  children: React.ReactNode;
  variant?: 'destructive' | 'primary' | 'informative' | 'warning';
  disabled?: boolean;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  pendingText?: string;
  requireConfirmationText?: string;
  confirmationPlaceholder?: string;
  handler: () => void | Promise<void>;
};

export function AlertDialogWrapper({
  children,
  variant = 'destructive',
  disabled = false,
  title = 'Hapus',
  description = 'Apakah Anda yakin ingin menghapus ini? Tindakan ini tidak dapat dibatalkan.',
  confirmText = 'Hapus',
  cancelText = 'Batal',
  pendingText = 'Menghapus...',
  requireConfirmationText,
  confirmationPlaceholder = 'Ketik untuk mengonfirmasi',
  handler,
}: AlertDialogWrapperProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [confirmationInput, setConfirmationInput] = useState('');

  const variantClass = {
    'destructive': {
      action: 'bg-red-600 hover:bg-red-700',
    },
    'primary': {
      action: 'bg-primary hover:bg-primary/90',
    },
    'informative': {
      action: 'bg-blue-600 hover:bg-blue-700',
    },
    'warning': {
      action: 'bg-orange-600 hover:bg-orange-700',
    },
  };

  // Reset confirmation input when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setConfirmationInput('');
    }
  }, [isOpen]);

  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      await handler();
      setIsOpen(false);
    } catch (error) {
      console.error('Error in AlertDialogWrapper handler:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Check if confirmation text is required and matches
  const isConfirmationValid = !requireConfirmationText || confirmationInput === requireConfirmationText;
  const isDisabled = disabled || isLoading || !isConfirmationValid;

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground whitespace-pre-line">
                {description}
              </div>
              {requireConfirmationText && (
                <div className="text-sm text-muted-foreground">
                  Ketik <span className="font-mono font-semibold text-destructive">&apos;{requireConfirmationText}&apos;</span> untuk mengonfirmasi
                </div>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>

        {requireConfirmationText && (
          <div className="px-6 pb-4">
            <Input
              type="text"
              placeholder={confirmationPlaceholder}
              value={confirmationInput}
              onChange={(e) => setConfirmationInput(e.target.value)}
              className="border-destructive focus:border-destructive focus:ring-destructive"
              disabled={isLoading}
              autoComplete="off"
            />
          </div>
        )}

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>{cancelText}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className={variantClass[variant].action}
            disabled={isDisabled}
          >
            {isLoading ? pendingText : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
