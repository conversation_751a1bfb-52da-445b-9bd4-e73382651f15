'use client';

import * as React from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, Filter, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { GoodsReceipt, GoodsReceiptStatus, QualityControlStatus } from '@/types/goods-receipt';
import { SupplierSelector } from '@/components/ui/supplier-selector';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  statusFilter?: GoodsReceiptStatus | '';
  onStatusFilterChange?: (value: GoodsReceiptStatus | '') => void;
  qualityStatusFilter?: QualityControlStatus | '';
  onQualityStatusFilterChange?: (value: QualityControlStatus | '') => void;
  supplierFilter?: string;
  onSupplierFilterChange?: (value: string) => void;
  isLoading?: boolean;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  searchValue = '',
  onSearchChange,
  statusFilter = '',
  onStatusFilterChange,
  qualityStatusFilter = '',
  onQualityStatusFilterChange,
  supplierFilter = '',
  onSupplierFilterChange,
  isLoading = false,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const hasActiveFilters = statusFilter || qualityStatusFilter || supplierFilter || searchValue;

  const clearFilters = () => {
    onSearchChange?.('');
    onStatusFilterChange?.('');
    onQualityStatusFilterChange?.('');
    onSupplierFilterChange?.('');
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-1 flex-col gap-2 sm:flex-row sm:items-center sm:space-x-2">
          {/* Search */}
          <div className="flex-1 sm:max-w-sm">
            <Input
              placeholder="Cari nomor penerimaan, supplier..."
              value={searchValue}
              onChange={(event) => onSearchChange?.(event.target.value)}
              className="h-8"
            />
          </div>

          {/* Status Filter */}
          <Select value={statusFilter} onValueChange={onStatusFilterChange}>
            <SelectTrigger className="h-8 w-full sm:w-[180px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Semua Status</SelectItem>
              <SelectItem value={GoodsReceiptStatus.PENDING}>Menunggu</SelectItem>
              <SelectItem value={GoodsReceiptStatus.IN_INSPECTION}>Inspeksi</SelectItem>
              <SelectItem value={GoodsReceiptStatus.APPROVED}>Disetujui</SelectItem>
              <SelectItem value={GoodsReceiptStatus.PARTIALLY_APPROVED}>Sebagian Disetujui</SelectItem>
              <SelectItem value={GoodsReceiptStatus.REJECTED}>Ditolak</SelectItem>
              <SelectItem value={GoodsReceiptStatus.COMPLETED}>Selesai</SelectItem>
            </SelectContent>
          </Select>

          {/* Quality Status Filter */}
          <Select value={qualityStatusFilter} onValueChange={onQualityStatusFilterChange}>
            <SelectTrigger className="h-8 w-full sm:w-[180px]">
              <SelectValue placeholder="Status Kualitas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Semua Status Kualitas</SelectItem>
              <SelectItem value={QualityControlStatus.PENDING}>Menunggu</SelectItem>
              <SelectItem value={QualityControlStatus.PASSED}>Lulus</SelectItem>
              <SelectItem value={QualityControlStatus.FAILED}>Gagal</SelectItem>
              <SelectItem value={QualityControlStatus.CONDITIONAL}>Bersyarat</SelectItem>
              <SelectItem value={QualityControlStatus.EXEMPTED}>Dikecualikan</SelectItem>
            </SelectContent>
          </Select>

          {/* Supplier Filter */}
          <div className="w-full sm:w-[200px]">
            <SupplierSelector
              value={supplierFilter}
              onValueChange={onSupplierFilterChange || (() => {})}
              placeholder="Pilih supplier..."
              allowNone
              noneLabel="Semua supplier"
              className="h-8"
            />
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="h-8 px-2 lg:px-3"
            >
              <X className="mr-2 h-4 w-4" />
              Reset
            </Button>
          )}
        </div>

        {/* Column Visibility */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="ml-auto h-8">
              <Filter className="mr-2 h-4 w-4" />
              Kolom
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[200px]">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(!!value)}
                  >
                    {column.id === 'receiptNumber' && 'Nomor Penerimaan'}
                    {column.id === 'supplier' && 'Supplier'}
                    {column.id === 'purchaseOrder' && 'Purchase Order'}
                    {column.id === 'receiptDate' && 'Tanggal Terima'}
                    {column.id === 'status' && 'Status'}
                    {column.id === 'qualityStatus' && 'Status Kualitas'}
                    {column.id === 'totalAmount' && 'Total'}
                    {column.id === 'createdAt' && 'Dibuat'}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {searchValue && (
            <Badge variant="secondary" className="gap-1">
              Pencarian: {searchValue}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 text-muted-foreground hover:text-foreground"
                onClick={() => onSearchChange?.('')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {statusFilter && (
            <Badge variant="secondary" className="gap-1">
              Status: {statusFilter}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 text-muted-foreground hover:text-foreground"
                onClick={() => onStatusFilterChange?.('')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          {qualityStatusFilter && (
            <Badge variant="secondary" className="gap-1">
              Status Kualitas: {qualityStatusFilter}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 text-muted-foreground hover:text-foreground"
                onClick={() => onQualityStatusFilterChange?.('')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border">
        <ScrollArea className="h-[600px]">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id} className="whitespace-nowrap">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    {columns.map((_, cellIndex) => (
                      <TableCell key={cellIndex}>
                        <div className="h-4 bg-muted animate-pulse rounded" />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="whitespace-nowrap">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Tidak ada data penerimaan barang.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} dari{' '}
          {table.getFilteredRowModel().rows.length} baris dipilih.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Baris per halaman</p>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={table.getState().pagination.pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Halaman {table.getState().pagination.pageIndex + 1} dari{' '}
            {table.getPageCount()}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Sebelumnya
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Selanjutnya
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
