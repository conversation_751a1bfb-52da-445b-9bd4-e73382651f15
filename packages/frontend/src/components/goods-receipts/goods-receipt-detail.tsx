'use client';

import { useGoodsReceipt } from '@/hooks/useGoodsReceipts';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { GoodsReceiptStatus, QualityControlStatus } from '@/types/goods-receipt';
import { formatCurrency } from '@/lib/utils/currency';
import { formatDate, formatDateTime } from '@/lib/utils/date';

interface GoodsReceiptDetailProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  goodsReceiptId: string;
}

function getStatusBadge(status: GoodsReceiptStatus) {
  const statusConfig = {
    [GoodsReceiptStatus.PENDING]: { label: 'Menunggu', variant: 'secondary' as const },
    [GoodsReceiptStatus.IN_INSPECTION]: { label: 'Inspeksi', variant: 'default' as const },
    [GoodsReceiptStatus.APPROVED]: { label: 'Disetujui', variant: 'default' as const },
    [GoodsReceiptStatus.PARTIALLY_APPROVED]: { label: 'Sebagian Disetujui', variant: 'outline' as const },
    [GoodsReceiptStatus.REJECTED]: { label: 'Ditolak', variant: 'destructive' as const },
    [GoodsReceiptStatus.COMPLETED]: { label: 'Selesai', variant: 'default' as const },
  };

  const config = statusConfig[status];
  return <Badge variant={config.variant}>{config.label}</Badge>;
}

function getQualityStatusBadge(status: QualityControlStatus) {
  const statusConfig = {
    [QualityControlStatus.PENDING]: { label: 'Menunggu', variant: 'secondary' as const },
    [QualityControlStatus.PASSED]: { label: 'Lulus', variant: 'default' as const },
    [QualityControlStatus.FAILED]: { label: 'Gagal', variant: 'destructive' as const },
    [QualityControlStatus.CONDITIONAL]: { label: 'Bersyarat', variant: 'outline' as const },
    [QualityControlStatus.EXEMPTED]: { label: 'Dikecualikan', variant: 'outline' as const },
  };

  const config = statusConfig[status];
  return <Badge variant={config.variant}>{config.label}</Badge>;
}

export function GoodsReceiptDetail({ open, onOpenChange, goodsReceiptId }: GoodsReceiptDetailProps) {
  const { data: goodsReceipt, isLoading, error } = useGoodsReceipt(goodsReceiptId);

  if (error) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Detail Penerimaan Barang</DialogTitle>
            <DialogDescription>
              Gagal memuat detail penerimaan barang
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Detail Penerimaan Barang</DialogTitle>
          <DialogDescription>
            {isLoading ? 'Memuat...' : `Detail untuk ${goodsReceipt?.receiptNumber}`}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh]">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ) : goodsReceipt ? (
            <div className="space-y-6">
              {/* Header Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold">Informasi Umum</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Nomor:</span>
                      <span className="font-medium">{goodsReceipt.receiptNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      {getStatusBadge(goodsReceipt.status)}
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status Kualitas:</span>
                      {getQualityStatusBadge(goodsReceipt.qualityStatus)}
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Tanggal Terima:</span>
                      <span>{formatDate(goodsReceipt.receiptDate)}</span>
                    </div>
                    {goodsReceipt.deliveryDate && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Tanggal Kirim:</span>
                        <span>{formatDate(goodsReceipt.deliveryDate)}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold">Supplier & PO</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Supplier:</span>
                      <span className="font-medium">{goodsReceipt.supplier.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Kode Supplier:</span>
                      <span>{goodsReceipt.supplier.code}</span>
                    </div>
                    {goodsReceipt.purchaseOrder && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">PO Number:</span>
                          <span className="font-medium">{goodsReceipt.purchaseOrder.orderNumber}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Tanggal PO:</span>
                          <span>{formatDate(goodsReceipt.purchaseOrder.orderDate)}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Delivery Information */}
              <div className="space-y-2">
                <h3 className="font-semibold">Informasi Pengiriman</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  {goodsReceipt.invoiceNumber && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">No. Invoice:</span>
                      <span>{goodsReceipt.invoiceNumber}</span>
                    </div>
                  )}
                  {goodsReceipt.deliveryNote && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">No. Surat Jalan:</span>
                      <span>{goodsReceipt.deliveryNote}</span>
                    </div>
                  )}
                  {goodsReceipt.deliveredBy && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Dikirim Oleh:</span>
                      <span>{goodsReceipt.deliveredBy}</span>
                    </div>
                  )}
                  {goodsReceipt.receivedBy && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Diterima Oleh:</span>
                      <span>{goodsReceipt.receivedBy}</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Items */}
              <div className="space-y-2">
                <h3 className="font-semibold">Item Penerimaan</h3>
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Produk</TableHead>
                        <TableHead>Batch</TableHead>
                        <TableHead>Exp. Date</TableHead>
                        <TableHead className="text-right">Qty Diterima</TableHead>
                        <TableHead className="text-right">Harga</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {goodsReceipt.items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className="font-medium">{item.product.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {item.product.code} • {item.unit.name}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {item.batchNumber || '-'}
                          </TableCell>
                          <TableCell>
                            {item.expiryDate ? formatDate(item.expiryDate) : '-'}
                          </TableCell>
                          <TableCell className="text-right">
                            {item.quantityReceived} {item.unit.abbreviation}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(item.unitPrice)}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(item.totalPrice)}
                          </TableCell>
                          <TableCell>
                            {getQualityStatusBadge(item.qualityStatus)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              <Separator />

              {/* Total */}
              <div className="flex justify-end">
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between gap-8">
                    <span className="font-semibold">Total:</span>
                    <span className="font-semibold text-lg">
                      {formatCurrency(goodsReceipt.totalAmount)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {(goodsReceipt.notes || goodsReceipt.qualityNotes) && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="font-semibold">Catatan</h3>
                    {goodsReceipt.notes && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Catatan Umum:</span>
                        <p className="mt-1">{goodsReceipt.notes}</p>
                      </div>
                    )}
                    {goodsReceipt.qualityNotes && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Catatan Kualitas:</span>
                        <p className="mt-1">{goodsReceipt.qualityNotes}</p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          ) : null}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
