'use client';

import { Package, CheckCircle, XCircle, Clock, TrendingUp, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useGoodsReceiptStats } from '@/hooks/useGoodsReceipts';
import { formatCurrency } from '@/lib/utils/currency';

export function GoodsReceiptStatsCards() {
  const { data: stats, isLoading, error } = useGoodsReceiptStats();

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <CardContent className="flex items-center justify-center py-8">
              <p className="text-sm text-muted-foreground">Gagal memuat data</p>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const qualityPassRatePercentage = stats.qualityPassRate * 100;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Receipts */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Penerimaan</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalReceipts.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            Semua penerimaan barang
          </p>
        </CardContent>
      </Card>

      {/* Pending Inspection */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Menunggu Inspeksi</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">
            {stats.pendingInspection.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Perlu kontrol kualitas
          </p>
        </CardContent>
      </Card>

      {/* Approved */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Disetujui</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {stats.approved.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Lulus kontrol kualitas
          </p>
        </CardContent>
      </Card>

      {/* Quality Pass Rate */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Tingkat Kelulusan</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {qualityPassRatePercentage.toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            Rata-rata kualitas baik
          </p>
        </CardContent>
      </Card>

      {/* Rejected (if any) */}
      {stats.rejected > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ditolak</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {stats.rejected.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Tidak memenuhi standar
            </p>
          </CardContent>
        </Card>
      )}

      {/* Average Inspection Time (if available) */}
      {stats.averageInspectionTime > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Inspeksi</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(stats.averageInspectionTime)}h
            </div>
            <p className="text-xs text-muted-foreground">
              Waktu proses inspeksi
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
