'use client';

import { useState, useEffect } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface AlertDialogWrapperProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: (input?: string) => void | Promise<void>;
  variant?: 'destructive' | 'primary' | 'informative' | 'warning';
  requiresInput?: boolean;
  inputPlaceholder?: string;
  multilineInput?: boolean;
}

export function AlertDialogWrapper({
  open,
  onOpenChange,
  title,
  description,
  confirmText = 'Konfirmasi',
  cancelText = 'Batal',
  onConfirm,
  variant = 'primary',
  requiresInput = false,
  inputPlaceholder = '',
  multilineInput = false,
}: AlertDialogWrapperProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const variantClass = {
    destructive: 'bg-destructive hover:bg-destructive/90',
    primary: 'bg-primary hover:bg-primary/90',
    informative: 'bg-blue-600 hover:bg-blue-700',
    warning: 'bg-orange-600 hover:bg-orange-700',
  };

  // Reset input when dialog opens/closes
  useEffect(() => {
    if (open) {
      setInputValue('');
    }
  }, [open]);

  const handleConfirm = async () => {
    if (requiresInput && !inputValue.trim()) {
      return;
    }

    try {
      setIsLoading(true);
      await onConfirm(requiresInput ? inputValue : undefined);
    } catch (error) {
      console.error('Error in AlertDialogWrapper:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isDisabled = isLoading || (requiresInput && !inputValue.trim());

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription className="whitespace-pre-line">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>

        {requiresInput && (
          <div className="px-6 pb-4">
            {multilineInput ? (
              <Textarea
                placeholder={inputPlaceholder}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                disabled={isLoading}
                className="min-h-[100px]"
              />
            ) : (
              <Input
                type="text"
                placeholder={inputPlaceholder}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                disabled={isLoading}
                autoComplete="off"
              />
            )}
          </div>
        )}

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className={variantClass[variant]}
            disabled={isDisabled}
          >
            {isLoading ? 'Memproses...' : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
