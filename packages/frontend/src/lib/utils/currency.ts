// Currency formatting utilities for Indonesian Rupiah

export function formatCurrency(amount: number | null | undefined): string {
  // Handle edge cases
  if (amount === null || amount === undefined || isNaN(amount)) {
    return 'Rp 0';
  }

  // Handle very large numbers (prevent overflow)
  const maxSafeAmount = Number.MAX_SAFE_INTEGER;
  const safeAmount = Math.abs(amount) > maxSafeAmount ? maxSafeAmount : amount;

  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(safeAmount);
}

export function formatNumber(num: number | null | undefined): string {
  // Handle edge cases
  if (num === null || num === undefined || isNaN(num)) {
    return '0';
  }

  return new Intl.NumberFormat('id-ID').format(num);
}

export function parseCurrency(currencyString: string): number | null {
  if (!currencyString || typeof currencyString !== 'string') {
    return null;
  }

  // Remove currency symbols, spaces, and dots (thousand separators)
  const cleanString = currencyString
    .replace(/[Rp\s]/g, '')
    .replace(/\./g, '')
    .replace(/,/g, '.');

  const num = Number(cleanString);
  return isNaN(num) ? null : num;
}
