// Goods Receipt Types and Interfaces

// Enums matching backend
export enum GoodsReceiptStatus {
  PENDING = 'PENDING',
  IN_INSPECTION = 'IN_INSPECTION',
  APPROVED = 'APPROVED',
  PARTIALLY_APPROVED = 'PARTIALLY_APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED',
}

export enum QualityControlStatus {
  PENDING = 'PENDING',
  PASSED = 'PASSED',
  FAILED = 'FAILED',
  CONDITIONAL = 'CONDITIONAL',
  EXEMPTED = 'EXEMPTED',
}

// Core interfaces
export interface GoodsReceiptItem {
  id: string;
  goodsReceiptId: string;
  purchaseOrderItemId?: string;
  productId: string;
  unitId: string;

  // Quantity Information
  quantityOrdered?: number;
  quantityReceived: number;
  quantityAccepted: number;
  quantityRejected: number;
  unitPrice: number;
  totalPrice: number;

  // Batch and Expiry Information
  batchNumber?: string;
  expiryDate?: string;
  manufacturingDate?: string;

  // Quality Control Information
  qualityStatus: QualityControlStatus;
  qualityNotes?: string;
  inspectionDate?: string;
  inspectionBy?: string;

  // Storage Information
  storageLocation?: string;
  storageCondition?: string;

  // Condition Information
  conditionOnReceipt?: string;
  damageNotes?: string;
  notes?: string;

  createdAt: string;
  updatedAt: string;

  // Relations
  product: {
    id: string;
    code: string;
    name: string;
    category: string;
    manufacturer?: string;
  };
  unit: {
    id: string;
    name: string;
    abbreviation: string;
    conversionFactor: number;
  };
  purchaseOrderItem?: {
    id: string;
    quantityOrdered: number;
    unitPrice: number;
  };
}

export interface GoodsReceipt {
  id: string;
  receiptNumber: string;
  purchaseOrderId?: string;
  supplierId: string;
  status: GoodsReceiptStatus;

  // Receipt Information
  receiptDate: string;
  deliveryDate?: string;
  invoiceNumber?: string;
  deliveryNote?: string;

  // Quality Control Information
  inspectionDate?: string;
  inspectionBy?: string;
  qualityStatus: QualityControlStatus;
  qualityNotes?: string;

  // Delivery Information
  deliveredBy?: string;
  receivedBy?: string;
  deliveryCondition?: string;

  // Financial Information
  totalAmount: number;

  // Metadata
  notes?: string;
  internalNotes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;

  // Relations
  supplier: {
    id: string;
    code: string;
    name: string;
    type: string;
    city?: string;
    phone?: string;
    email?: string;
  };
  purchaseOrder?: {
    id: string;
    orderNumber: string;
    status: string;
    orderDate: string;
    totalAmount: number;
  };
  createdByUser?: {
    id: string;
    name: string;
    email: string;
  };
  updatedByUser?: {
    id: string;
    name: string;
    email: string;
  };
  inspectionByUser?: {
    id: string;
    name: string;
    email: string;
  };
  items: GoodsReceiptItem[];
}

// Query parameters
export interface GoodsReceiptQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  supplierId?: string;
  purchaseOrderId?: string;
  status?: GoodsReceiptStatus;
  qualityStatus?: QualityControlStatus;
  receiptDateFrom?: string;
  receiptDateTo?: string;
  deliveryDateFrom?: string;
  deliveryDateTo?: string;
  inspectionBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// API Response types
export interface GoodsReceiptListResponse {
  data: GoodsReceipt[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface GoodsReceiptStatsResponse {
  totalReceipts: number;
  pendingInspection: number;
  approved: number;
  rejected: number;
  qualityPassRate: number;
  averageInspectionTime: number;
  statusStats: Record<string, number>;
  recentReceipts: GoodsReceipt[];
}

// Create DTOs
export interface CreateGoodsReceiptItemDto {
  purchaseOrderItemId?: string;
  productId: string;
  unitId: string;
  quantityOrdered?: number;
  quantityReceived: number;
  unitPrice: number;
  batchNumber?: string;
  expiryDate?: string;
  manufacturingDate?: string;
  qualityStatus?: QualityControlStatus;
  qualityNotes?: string;
  inspectionDate?: string;
  inspectionBy?: string;
  storageLocation?: string;
  storageCondition?: string;
  conditionOnReceipt?: string;
  damageNotes?: string;
  notes?: string;
}

export interface CreateGoodsReceiptDto {
  receiptNumber?: string;
  purchaseOrderId?: string;
  supplierId: string;
  status?: GoodsReceiptStatus;
  receiptDate?: string;
  deliveryDate?: string;
  invoiceNumber?: string;
  deliveryNote?: string;
  inspectionDate?: string;
  inspectionBy?: string;
  qualityStatus?: QualityControlStatus;
  qualityNotes?: string;
  deliveredBy?: string;
  receivedBy?: string;
  deliveryCondition?: string;
  notes?: string;
  internalNotes?: string;
  items: CreateGoodsReceiptItemDto[];
}

// Update DTOs
export interface UpdateGoodsReceiptItemDto {
  id?: string;
  quantityAccepted?: number;
  quantityRejected?: number;
  qualityStatus?: QualityControlStatus;
  qualityNotes?: string;
  inspectionDate?: string;
  inspectionBy?: string;
  storageLocation?: string;
  conditionOnReceipt?: string;
  damageNotes?: string;
}

export interface UpdateGoodsReceiptDto {
  status?: GoodsReceiptStatus;
  receiptDate?: string;
  deliveryDate?: string;
  invoiceNumber?: string;
  deliveryNote?: string;
  inspectionDate?: string;
  inspectionBy?: string;
  qualityStatus?: QualityControlStatus;
  qualityNotes?: string;
  deliveredBy?: string;
  receivedBy?: string;
  deliveryCondition?: string;
  notes?: string;
  internalNotes?: string;
  items?: UpdateGoodsReceiptItemDto[];
}

// Quality Control DTOs
export interface QualityControlUpdateDto {
  qualityStatus: QualityControlStatus;
  qualityNotes?: string;
  inspectionDate?: string;
  temperatureCheck?: boolean;
  packagingCheck?: boolean;
  documentationCheck?: boolean;
  bpomCheck?: boolean;
}

export interface GoodsReceiptStatusUpdateDto {
  status: GoodsReceiptStatus;
  notes?: string;
}

export interface RejectGoodsReceiptDto {
  reason: string;
}

// Form types for frontend
export interface GoodsReceiptFormItem {
  productId: string;
  unitId: string;
  quantityOrdered?: number;
  quantityReceived: number;
  unitPrice: number;
  batchNumber?: string;
  expiryDate?: string;
  manufacturingDate?: string;
  storageLocation?: string;
  conditionOnReceipt?: string;
  notes?: string;

  // Computed fields for display
  totalPrice?: number;

  // Product info for display
  product?: {
    id: string;
    code: string;
    name: string;
    category: string;
    manufacturer?: string;
  };
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
    conversionFactor: number;
  };
}

export interface GoodsReceiptFormData {
  purchaseOrderId?: string;
  supplierId: string;
  receiptDate: string;
  deliveryDate?: string;
  invoiceNumber?: string;
  deliveryNote?: string;
  deliveredBy?: string;
  receivedBy?: string;
  deliveryCondition?: string;
  notes?: string;
  internalNotes?: string;
  items: GoodsReceiptFormItem[];

  // Computed totals
  totalAmount?: number;
}

// Quality Control Form Data
export interface QualityControlFormData {
  qualityStatus: QualityControlStatus;
  qualityNotes?: string;
  inspectionDate: string;
  temperatureCheck?: boolean;
  packagingCheck?: boolean;
  documentationCheck?: boolean;
  bpomCheck?: boolean;
}
